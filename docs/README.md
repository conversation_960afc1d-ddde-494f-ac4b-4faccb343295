# Documentação do Vanna AI Odoo Assistant

Bem-vindo à documentação do Vanna AI Odoo Assistant. Esta pasta contém documentação detalhada sobre as funcionalidades do sistema.

## Índice

### Detecção de Anomalias
- [Documentação completa de detecção de anomalias](anomaly_detection.md) - Visão geral, algoritmos, casos de uso e interpretação
- [Exemplos práticos de detecção de anomalias](anomaly_detection_examples.md) - Cenários de negócio com consultas SQL e interpretações
- [Guia de referência dos algoritmos](anomaly_detection_algorithms.md) - Comparação detalhada dos algoritmos e guia de seleção

### Outros Recursos
- [Imagens e diagramas](images/) - Recursos visuais usados na documentação

## Como Usar Esta Documentação

1. Comece com o [README principal](../README.md) para uma visão geral do sistema
2. Consulte a documentação específica de cada funcionalidade para informações detalhadas
3. Veja os exemplos práticos para entender como aplicar as funcionalidades em cenários reais
4. Use os guias de referência para informações técnicas detalhadas

## Contribuindo para a Documentação

Se você deseja contribuir para esta documentação:

1. Faça um fork do repositório
2. Crie uma branch para suas alterações
3. Adicione ou modifique os arquivos de documentação
4. Envie um pull request com suas alterações

Certifique-se de seguir o estilo e formato existentes para manter a consistência da documentação.
