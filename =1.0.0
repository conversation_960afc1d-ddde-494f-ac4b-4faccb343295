Requirement already satisfied: openai in ./vanna-aug-venv/lib/python3.11/site-packages (0.28.0)
Collecting openai
  Downloading openai-1.78.0-py3-none-any.whl.metadata (25 kB)
Requirement already satisfied: anyio<5,>=3.5.0 in ./vanna-aug-venv/lib/python3.11/site-packages (from openai) (4.9.0)
Requirement already satisfied: distro<2,>=1.7.0 in ./vanna-aug-venv/lib/python3.11/site-packages (from openai) (1.9.0)
Requirement already satisfied: httpx<1,>=0.23.0 in ./vanna-aug-venv/lib/python3.11/site-packages (from openai) (0.28.1)
Requirement already satisfied: jiter<1,>=0.4.0 in ./vanna-aug-venv/lib/python3.11/site-packages (from openai) (0.9.0)
Requirement already satisfied: pydantic<3,>=1.9.0 in ./vanna-aug-venv/lib/python3.11/site-packages (from openai) (2.11.4)
Requirement already satisfied: sniffio in ./vanna-aug-venv/lib/python3.11/site-packages (from openai) (1.3.1)
Requirement already satisfied: tqdm>4 in ./vanna-aug-venv/lib/python3.11/site-packages (from openai) (4.67.1)
Requirement already satisfied: typing-extensions<5,>=4.11 in ./vanna-aug-venv/lib/python3.11/site-packages (from openai) (4.13.2)
Requirement already satisfied: idna>=2.8 in ./vanna-aug-venv/lib/python3.11/site-packages (from anyio<5,>=3.5.0->openai) (3.10)
Requirement already satisfied: certifi in ./vanna-aug-venv/lib/python3.11/site-packages (from httpx<1,>=0.23.0->openai) (2025.4.26)
Requirement already satisfied: httpcore==1.* in ./vanna-aug-venv/lib/python3.11/site-packages (from httpx<1,>=0.23.0->openai) (1.0.9)
Requirement already satisfied: h11>=0.16 in ./vanna-aug-venv/lib/python3.11/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in ./vanna-aug-venv/lib/python3.11/site-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in ./vanna-aug-venv/lib/python3.11/site-packages (from pydantic<3,>=1.9.0->openai) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in ./vanna-aug-venv/lib/python3.11/site-packages (from pydantic<3,>=1.9.0->openai) (0.4.0)
Downloading openai-1.78.0-py3-none-any.whl (680 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 680.4/680.4 kB 6.7 MB/s eta 0:00:00
Installing collected packages: openai
  Attempting uninstall: openai
    Found existing installation: openai 0.28.0
    Uninstalling openai-0.28.0:
      Successfully uninstalled openai-0.28.0
Successfully installed openai-1.78.0
