# Environment variables
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# ChromaDB data
data/
chroma.sqlite3
**/chroma.sqlite3

# Logs
*.log

# Virtual Environment
venv/
.venv/
ENV/
vanna-aug-venv/

# Poetry
.poetry/
poetry.toml

# IDE files
.idea/
.vscode/
*.swp
*.swo
test-reports/
coverage.xml
