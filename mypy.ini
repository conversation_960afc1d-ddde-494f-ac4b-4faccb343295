[mypy]
python_version = 3.11
warn_return_any = False
warn_unused_configs = False
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = False
disallow_untyped_decorators = False
no_implicit_optional = False
strict_optional = False
follow_imports = skip
ignore_missing_imports = True
disallow_any_unimported = False
disallow_any_expr = False
disallow_any_decorated = False
disallow_any_explicit = False
disallow_any_generics = False
disallow_subclassing_any = False

# Ignorar erros em arquivos específicos
exclude = app/

[mypy.plugins.numpy.*]
follow_imports = skip
ignore_missing_imports = True

[mypy.plugins.pandas.*]
follow_imports = skip
ignore_missing_imports = True

[mypy.plugins.streamlit.*]
follow_imports = skip
ignore_missing_imports = True

[mypy.plugins.dateutil.*]
follow_imports = skip
ignore_missing_imports = True
