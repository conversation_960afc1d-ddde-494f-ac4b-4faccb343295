altair==5.5.0 ; python_version >= "3.11" and python_version < "4.0"
annotated-types==0.7.0 ; python_version >= "3.11" and python_version < "4.0"
anyio==4.9.0 ; python_version >= "3.11" and python_version < "4.0"
asgiref==3.8.1 ; python_version >= "3.11" and python_version < "4.0"
attrs==25.3.0 ; python_version >= "3.11" and python_version < "4.0"
backoff==2.2.1 ; python_version >= "3.11" and python_version < "4.0"
bcrypt==4.3.0 ; python_version >= "3.11" and python_version < "4.0"
blinker==1.9.0 ; python_version >= "3.11" and python_version < "4.0"
build==1.2.2.post1 ; python_version >= "3.11" and python_version < "4.0"
cachetools==5.5.2 ; python_version >= "3.11" and python_version < "4.0"
certifi==2025.4.26 ; python_version >= "3.11" and python_version < "4.0"
charset-normalizer==3.4.2 ; python_version >= "3.11" and python_version < "4.0"
chromadb==1.0.8 ; python_version >= "3.11" and python_version < "4.0"
click==8.1.8 ; python_version >= "3.11" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.11" and python_version < "4.0" and (os_name == "nt" or platform_system == "Windows" or sys_platform == "win32")
coloredlogs==15.0.1 ; python_version >= "3.11" and python_version < "4.0"
contourpy==1.3.2 ; python_version >= "3.11" and python_version < "4.0"
cycler==0.12.1 ; python_version >= "3.11" and python_version < "4.0"
deprecated==1.2.18 ; python_version >= "3.11" and python_version < "4.0"
distro==1.9.0 ; python_version >= "3.11" and python_version < "4.0"
durationpy==0.9 ; python_version >= "3.11" and python_version < "4.0"
fastapi==0.115.9 ; python_version >= "3.11" and python_version < "4.0"
filelock==3.18.0 ; python_version >= "3.11" and python_version < "4.0"
flasgger==0.9.7.1 ; python_version >= "3.11" and python_version < "4.0"
flask-sock==0.7.0 ; python_version >= "3.11" and python_version < "4.0"
flask==3.1.0 ; python_version >= "3.11" and python_version < "4.0"
flatbuffers==25.2.10 ; python_version >= "3.11" and python_version < "4.0"
fonttools==4.58.0 ; python_version >= "3.11" and python_version < "4.0"
fsspec==2025.3.2 ; python_version >= "3.11" and python_version < "4.0"
gitdb==4.0.12 ; python_version >= "3.11" and python_version < "4.0"
gitpython==3.1.44 ; python_version >= "3.11" and python_version < "4.0"
google-auth==2.40.1 ; python_version >= "3.11" and python_version < "4.0"
googleapis-common-protos==1.70.0 ; python_version >= "3.11" and python_version < "4.0"
greenlet==3.2.2 ; python_version < "3.14" and (platform_machine == "aarch64" or platform_machine == "ppc64le" or platform_machine == "x86_64" or platform_machine == "amd64" or platform_machine == "AMD64" or platform_machine == "win32" or platform_machine == "WIN32") and python_version >= "3.11"
grpcio==1.71.0 ; python_version >= "3.11" and python_version < "4.0"
h11==0.16.0 ; python_version >= "3.11" and python_version < "4.0"
hf-xet==1.1.0 ; python_version >= "3.11" and python_version < "4.0" and (platform_machine == "x86_64" or platform_machine == "amd64" or platform_machine == "arm64" or platform_machine == "aarch64")
httpcore==1.0.9 ; python_version >= "3.11" and python_version < "4.0"
httptools==0.6.4 ; python_version >= "3.11" and python_version < "4.0"
httpx==0.28.1 ; python_version >= "3.11" and python_version < "4.0"
huggingface-hub==0.31.1 ; python_version >= "3.11" and python_version < "4.0"
humanfriendly==10.0 ; python_version >= "3.11" and python_version < "4.0"
idna==3.10 ; python_version >= "3.11" and python_version < "4.0"
importlib-metadata==8.6.1 ; python_version >= "3.11" and python_version < "4.0"
importlib-resources==6.5.2 ; python_version >= "3.11" and python_version < "4.0"
itsdangerous==2.2.0 ; python_version >= "3.11" and python_version < "4.0"
jinja2==3.1.6 ; python_version >= "3.11" and python_version < "4.0"
jiter==0.9.0 ; python_version >= "3.11" and python_version < "4.0"
joblib==1.5.0 ; python_version >= "3.11" and python_version < "4.0"
jsonschema-specifications==2025.4.1 ; python_version >= "3.11" and python_version < "4.0"
jsonschema==4.23.0 ; python_version >= "3.11" and python_version < "4.0"
kaleido==0.2.1 ; python_version >= "3.11" and python_version < "4.0"
kiwisolver==1.4.8 ; python_version >= "3.11" and python_version < "4.0"
kubernetes==32.0.1 ; python_version >= "3.11" and python_version < "4.0"
llvmlite==0.44.0 ; python_version >= "3.11" and python_version < "4.0"
markdown-it-py==3.0.0 ; python_version >= "3.11" and python_version < "4.0"
markupsafe==3.0.2 ; python_version >= "3.11" and python_version < "4.0"
matplotlib==3.10.3 ; python_version >= "3.11" and python_version < "4.0"
mdurl==0.1.2 ; python_version >= "3.11" and python_version < "4.0"
mistune==3.1.3 ; python_version >= "3.11" and python_version < "4.0"
mmh3==5.1.0 ; python_version >= "3.11" and python_version < "4.0"
mpmath==1.3.0 ; python_version >= "3.11" and python_version < "4.0"
narwhals==1.38.2 ; python_version >= "3.11" and python_version < "4.0"
numba==0.61.2 ; python_version >= "3.11" and python_version < "4.0"
numpy==2.2.5 ; python_version >= "3.11" and python_version < "4.0"
oauthlib==3.2.2 ; python_version >= "3.11" and python_version < "4.0"
onnxruntime==1.22.0 ; python_version >= "3.11" and python_version < "4.0"
openai==1.78.0 ; python_version >= "3.11" and python_version < "4.0"
opentelemetry-api==1.33.0 ; python_version >= "3.11" and python_version < "4.0"
opentelemetry-exporter-otlp-proto-grpc==1.15.0 ; python_version >= "3.11" and python_version < "4.0"
opentelemetry-instrumentation-asgi==0.54b0 ; python_version >= "3.11" and python_version < "4.0"
opentelemetry-instrumentation-fastapi==0.54b0 ; python_version >= "3.11" and python_version < "4.0"
opentelemetry-instrumentation==0.54b0 ; python_version >= "3.11" and python_version < "4.0"
opentelemetry-proto==1.15.0 ; python_version >= "3.11" and python_version < "4.0"
opentelemetry-sdk==1.33.0 ; python_version >= "3.11" and python_version < "4.0"
opentelemetry-semantic-conventions==0.54b0 ; python_version >= "3.11" and python_version < "4.0"
opentelemetry-util-http==0.54b0 ; python_version >= "3.11" and python_version < "4.0"
orjson==3.10.18 ; python_version >= "3.11" and python_version < "4.0"
overrides==7.7.0 ; python_version >= "3.11" and python_version < "4.0"
packaging==24.2 ; python_version >= "3.11" and python_version < "4.0"
pandas==2.2.3 ; python_version >= "3.11" and python_version < "4.0"
patsy==1.0.1 ; python_version >= "3.11" and python_version < "4.0"
pillow==10.4.0 ; python_version >= "3.11" and python_version < "4.0"
plotly==6.0.1 ; python_version >= "3.11" and python_version < "4.0"
postgres==4.0 ; python_version >= "3.11" and python_version < "4.0"
posthog==4.0.1 ; python_version >= "3.11" and python_version < "4.0"
protobuf==3.20.3 ; python_version >= "3.11" and python_version < "4.0"
psycopg2-binary==2.9.10 ; python_version >= "3.11" and python_version < "4.0"
psycopg2-pool==1.2 ; python_version >= "3.11" and python_version < "4.0"
pyarrow==20.0.0 ; python_version >= "3.11" and python_version < "4.0"
pyasn1-modules==0.4.2 ; python_version >= "3.11" and python_version < "4.0"
pyasn1==0.6.1 ; python_version >= "3.11" and python_version < "4.0"
pydantic-core==2.33.2 ; python_version >= "3.11" and python_version < "4.0"
pydantic==2.11.4 ; python_version >= "3.11" and python_version < "4.0"
pydeck==0.9.1 ; python_version >= "3.11" and python_version < "4.0"
pygments==2.19.1 ; python_version >= "3.11" and python_version < "4.0"
pyod==2.0.5 ; python_version >= "3.11" and python_version < "4.0"
pyparsing==3.2.3 ; python_version >= "3.11" and python_version < "4.0"
pypika==0.48.9 ; python_version >= "3.11" and python_version < "4.0"
pyproject-hooks==1.2.0 ; python_version >= "3.11" and python_version < "4.0"
pyreadline3==3.5.4 ; sys_platform == "win32" and python_version >= "3.11" and python_version < "4.0"
python-dateutil==2.9.0.post0 ; python_version >= "3.11" and python_version < "4.0"
python-dotenv==1.1.0 ; python_version >= "3.11" and python_version < "4.0"
python-slugify==8.0.4 ; python_version >= "3.11" and python_version < "4.0"
pytz==2025.2 ; python_version >= "3.11" and python_version < "4.0"
pyyaml==6.0.2 ; python_version >= "3.11" and python_version < "4.0"
referencing==0.36.2 ; python_version >= "3.11" and python_version < "4.0"
regex==2024.11.6 ; python_version >= "3.11" and python_version < "4.0"
requests-oauthlib==2.0.0 ; python_version >= "3.11" and python_version < "4.0"
requests==2.32.3 ; python_version >= "3.11" and python_version < "4.0"
rich==14.0.0 ; python_version >= "3.11" and python_version < "4.0"
rpds-py==0.24.0 ; python_version >= "3.11" and python_version < "4.0"
rsa==4.9.1 ; python_version >= "3.11" and python_version < "4"
scikit-learn==1.6.1 ; python_version >= "3.11" and python_version < "4.0"
scipy==1.15.3 ; python_version >= "3.11" and python_version < "4.0"
shellingham==1.5.4 ; python_version >= "3.11" and python_version < "4.0"
simple-websocket==1.1.0 ; python_version >= "3.11" and python_version < "4.0"
six==1.17.0 ; python_version >= "3.11" and python_version < "4.0"
smmap==5.0.2 ; python_version >= "3.11" and python_version < "4.0"
sniffio==1.3.1 ; python_version >= "3.11" and python_version < "4.0"
sqlalchemy==2.0.40 ; python_version >= "3.11" and python_version < "4.0"
sqlparse==0.4.4 ; python_version >= "3.11" and python_version < "4.0"
starlette==0.45.3 ; python_version >= "3.11" and python_version < "4.0"
statsmodels==0.14.4 ; python_version >= "3.11" and python_version < "4.0"
streamlit==1.45.0 ; python_version >= "3.11" and python_version < "4.0"
sympy==1.14.0 ; python_version >= "3.11" and python_version < "4.0"
tabulate==0.9.0 ; python_version >= "3.11" and python_version < "4.0"
tenacity==9.1.2 ; python_version >= "3.11" and python_version < "4.0"
text-unidecode==1.3 ; python_version >= "3.11" and python_version < "4.0"
threadpoolctl==3.6.0 ; python_version >= "3.11" and python_version < "4.0"
tiktoken==0.9.0 ; python_version >= "3.11" and python_version < "4.0"
tokenizers==0.21.1 ; python_version >= "3.11" and python_version < "4.0"
toml==0.10.2 ; python_version >= "3.11" and python_version < "4.0"
tornado==6.4.2 ; python_version >= "3.11" and python_version < "4.0"
tqdm==4.67.1 ; python_version >= "3.11" and python_version < "4.0"
typer==0.15.3 ; python_version >= "3.11" and python_version < "4.0"
typing-extensions==4.13.2 ; python_version >= "3.11" and python_version < "4.0"
typing-inspection==0.4.0 ; python_version >= "3.11" and python_version < "4.0"
tzdata==2025.2 ; python_version >= "3.11" and python_version < "4.0"
urllib3==2.4.0 ; python_version >= "3.11" and python_version < "4.0"
uvicorn[standard]==0.34.2 ; python_version >= "3.11" and python_version < "4.0"
uvloop==0.21.0 ; (sys_platform != "win32" and sys_platform != "cygwin") and platform_python_implementation != "PyPy" and python_version >= "3.11" and python_version < "4.0"
vanna==0.7.9 ; python_version >= "3.11" and python_version < "4.0"
watchdog==6.0.0 ; python_version >= "3.11" and python_version < "4.0" and platform_system != "Darwin"
watchfiles==1.0.5 ; python_version >= "3.11" and python_version < "4.0"
websocket-client==1.8.0 ; python_version >= "3.11" and python_version < "4.0"
websockets==15.0.1 ; python_version >= "3.11" and python_version < "4.0"
werkzeug==3.1.3 ; python_version >= "3.11" and python_version < "4.0"
wrapt==1.17.2 ; python_version >= "3.11" and python_version < "4.0"
wsproto==1.2.0 ; python_version >= "3.11" and python_version < "4.0"
xlsxwriter==3.2.3 ; python_version >= "3.11" and python_version < "4.0"
zipp==3.21.0 ; python_version >= "3.11" and python_version < "4.0"
