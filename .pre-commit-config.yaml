repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
    -   id: check-ast
    -   id: check-json
    -   id: check-merge-conflict
    -   id: detect-private-key

-   repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
    -   id: black
        language_version: python3

-   repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
    -   id: flake8
        additional_dependencies: [flake8-docstrings]
        args: ["--config=.flake8"]

-   repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
    -   id: isort
        args: ["--profile", "black"]

# Temporariamente desabilitado até resolvermos os problemas de tipo
# -   repo: https://github.com/pre-commit/mirrors-mypy
#     rev: v1.3.0
#     hooks:
#     -   id: mypy
#         additional_dependencies: [types-requests, types-PyYAML]
#         args: ["--config-file=mypy.ini"]
