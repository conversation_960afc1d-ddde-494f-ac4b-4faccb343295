## <PERSON><PERSON><PERSON><PERSON>

<!-- <PERSON><PERSON><PERSON> as alterações que você fez -->

## Tipo de alteração

<!-- <PERSON><PERSON> as opções aplicáveis com um "x" -->

- [ ] Correção de bug (alteração que corrige um problema)
- [ ] Nova funcionalidade (alteração que adiciona funcionalidade)
- [ ] Alteração significativa (correção ou recurso que faria com que a funcionalidade existente mudasse)
- [ ] Melhoria de documentação
- [ ] Refatoração de código (sem alteração de funcionalidade)
- [ ] Melhoria de desempenho
- [ ] Melhoria de testes

## Checklist

<!-- <PERSON><PERSON> as opções aplicáveis com um "x" -->

- [ ] Meu código segue o estilo de código deste projeto
- [ ] Eu adicionei testes para cobrir minhas alterações
- [ ] Todos os testes novos e existentes passaram
- [ ] Eu atualizei a documentação conforme necessário
- [ ] Minhas alterações não geram novos avisos ou erros
- [ ] Eu verifiquei meu código e corrigi quaisquer erros de digitação

## Capturas de tela (se aplicável)

<!-- Adicione capturas de tela para ajudar a explicar seu problema, se aplicável -->

## Contexto adicional

<!-- Adicione qualquer outro contexto sobre o problema aqui -->
