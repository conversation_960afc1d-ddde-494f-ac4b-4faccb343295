# OpenAI Settings
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4  # Define o modelo OpenAI a ser usado (ex: gpt-4, gpt-4o, gpt-3.5-turbo)
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002  # Define o modelo de embeddings (ex: text-embedding-ada-002, text-embedding-3-small, text-embedding-3-large)

# Security Settings
ALLOW_LLM_TO_SEE_DATA=false  # Set to true to allow the LLM to see your data for generating summaries

# Odoo PostgreSQL Database Connection
# Treinar o odoo com usúario Administrador, depois alterar para usuário somente leitura.
# Função get_table_relationships, foi implementada Relacionamento por conveção de nomenclatura, para usúario leitura, mas limita alguns relacionamentos.
ODOO_DB_HOST=your_odoo_db_host
ODOO_DB_PORT=5432
ODOO_DB_NAME=your_odoo_db_name
ODOO_DB_USER=your_odoo_db_user
ODOO_DB_PASSWORD=your_odoo_db_password

# ChromaDB Settings
# IMPORTANTE: Este diretório deve corresponder ao caminho montado no volume do docker-compose.yml
# Não altere este valor a menos que você também altere o docker-compose.yml
CHROMA_PERSIST_DIRECTORY=/app/data/chromadb

# Não é mais necessário configurar o servidor ChromaDB, pois estamos usando o cliente persistente local
